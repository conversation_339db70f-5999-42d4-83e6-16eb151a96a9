const { PrismaClient } = require('@prisma/client');
const { calculateBirth<PERSON><PERSON>, calculateEnhancedBirthChart, calculateSriLankanHoroscope } = require('../src/lib/astrology');

async function testNavamsaCalculation() {
  console.log('🧪 COMPREHENSIVE HOROSCOPE VALIDATION TEST');
  console.log('=' .repeat(60));
  console.log('📅 Birthday: 10/16/2024');
  console.log('⏰ Birth Time: 09:18 AM (Sri Lanka Time)');
  console.log('📍 Birth place: Peradeniya, Sri Lanka');
  console.log('🌍 Coordinates: 7.2667°N, 80.5913°E');
  console.log('');
  console.log('🎯 EXPECTED RESULTS FROM REAL SRI LANKAN HOROSCOPE:');
  console.log('   ✅ Expected Lagna: Scorpio');
  console.log('   ✅ Expected Navamsa: Sagittarius');
  console.log('=' .repeat(60));

  try {
    const prisma = new PrismaClient();

    // Create or update test user with specific birth details
    const testUser = await prisma.user.upsert({
      where: { qrToken: 'navamsa-test-2024' },
      update: {
        name: 'Navamsa Test User',
        birthDate: new Date('2024-10-16'),
        birthTime: '09:18',
        birthPlace: 'Peradeniya, Sri Lanka',
        birthLatitude: 7.2667,
        birthLongitude: 80.5913,
        zodiacSign: 'libra', // We'll verify this
        languagePreference: 'en'
      },
      create: {
        name: 'Navamsa Test User',
        email: '<EMAIL>',
        birthDate: new Date('2024-10-16'),
        birthTime: '09:18',
        birthPlace: 'Peradeniya, Sri Lanka',
        birthLatitude: 7.2667,
        birthLongitude: 80.5913,
        zodiacSign: 'libra',
        languagePreference: 'en',
        qrToken: 'navamsa-test-2024'
      }
    });

    console.log('✅ Test user created/updated:', testUser.id);

    // Create QR code mapping if it doesn't exist
    await prisma.qrCodeMapping.upsert({
      where: { qrToken: 'navamsa-test-2024' },
      update: { userId: testUser.id },
      create: {
        qrToken: 'navamsa-test-2024',
        userId: testUser.id,
        scanCount: 0
      }
    });

    // Calculate birth chart with the exact details
    const birthDateTime = new Date('2024-10-16T09:18:00');
    const timezone = 'Asia/Colombo';

    console.log('\n🔄 STEP 1: Testing Enhanced Birth Chart Calculation...');
    console.log('📊 Birth DateTime:', birthDateTime.toISOString());
    console.log('🕐 Timezone:', timezone);

    const birthDetails = {
      birthDate: birthDateTime,
      latitude: testUser.birthLatitude,
      longitude: testUser.birthLongitude,
      timezone: timezone
    };

    // Test enhanced birth chart calculation
    const enhancedChartData = await calculateEnhancedBirthChart(birthDetails);

    console.log('\n🔄 STEP 2: Testing Sri Lankan Horoscope Calculation...');
    let sriLankanData = null;
    try {
      sriLankanData = await calculateSriLankanHoroscope(birthDetails);
      console.log('✅ Sri Lankan horoscope calculation successful');
    } catch (error) {
      console.error('❌ Sri Lankan horoscope calculation failed:', error.message);
    }

    // Also test the basic chart for comparison
    const basicChartData = await calculateBirthChart(
      birthDateTime,
      testUser.birthLatitude,
      testUser.birthLongitude,
      timezone
    );

    console.log('\n📈 STEP 3: VALIDATION RESULTS ANALYSIS');
    console.log('=' .repeat(60));

    // Test results tracking
    let lagnaTestPassed = false;
    let navamsaTestPassed = false;

    // 1. Test Sri Lankan Horoscope Data (Most Accurate)
    if (sriLankanData) {
      console.log('\n🇱🇰 SRI LANKAN HOROSCOPE CALCULATION RESULTS:');
      console.log('=' .repeat(50));
      console.log('🌅 Lagna (Ascendant):', sriLankanData.lagna);
      console.log('📊 Navamsa Chart:', sriLankanData.navamsa_chart);

      // Validate Lagna
      if (sriLankanData.lagna && sriLankanData.lagna.rashi) {
        console.log(`\n🎯 LAGNA VALIDATION:`);
        console.log(`   Expected: Scorpio`);
        console.log(`   Calculated: ${sriLankanData.lagna.rashi}`);
        lagnaTestPassed = sriLankanData.lagna.rashi === 'Scorpio';
        console.log(`   Result: ${lagnaTestPassed ? '✅ PASSED' : '❌ FAILED'}`);
      }

      // Validate Navamsa - check first house for ascendant
      if (sriLankanData.navamsa_chart && sriLankanData.navamsa_chart.house1) {
        const navamsaAscendantPlanets = sriLankanData.navamsa_chart.house1;
        console.log(`\n🎯 NAVAMSA VALIDATION:`);
        console.log(`   Expected: Sagittarius`);
        console.log(`   Navamsa House 1 planets: ${navamsaAscendantPlanets.join(', ') || 'None'}`);
        // For Navamsa, we need to check the ascendant's position, not just house 1
        console.log(`   Note: Need to check ascendant's navamsa position specifically`);
      }
    }

    // 2. Test Enhanced Chart Data
    console.log('\n🔮 ENHANCED CHART CALCULATION RESULTS:');
    console.log('=' .repeat(50));
    console.log('🌅 Ascendant (Lagna):', enhancedChartData.ascendant);
    console.log('🌙 Moon Sign:', enhancedChartData.moonSign);
    console.log('☀️ Sun Sign:', enhancedChartData.sunSign);

    // Validate Enhanced Chart Lagna
    if (enhancedChartData.ascendant) {
      console.log(`\n🎯 ENHANCED LAGNA VALIDATION:`);
      console.log(`   Expected: Scorpio`);
      console.log(`   Calculated: ${enhancedChartData.ascendant}`);
      const enhancedLagnaTest = enhancedChartData.ascendant === 'Scorpio';
      console.log(`   Result: ${enhancedLagnaTest ? '✅ PASSED' : '❌ FAILED'}`);
      if (!lagnaTestPassed) lagnaTestPassed = enhancedLagnaTest;
    }

    console.log('\n📊 NAVAMSA CHART ANALYSIS:');
    console.log('=' .repeat(50));
    if (enhancedChartData.navamsaChart && enhancedChartData.navamsaChart.houses) {
      console.log('🏠 Navamsa Houses:');
      enhancedChartData.navamsaChart.houses.forEach((house, index) => {
        console.log(`   House ${index + 1}: ${house.sign} - Planets: ${house.planets.map(p => p.name).join(', ') || 'None'}`);
      });

      // Check the first house sign (Navamsa Ascendant)
      const navamsaAscendant = enhancedChartData.navamsaChart.houses[0].sign;
      console.log(`\n🎯 ENHANCED NAVAMSA VALIDATION:`);
      console.log(`   Expected: Sagittarius`);
      console.log(`   Calculated: ${navamsaAscendant}`);
      navamsaTestPassed = navamsaAscendant === 'Sagittarius';
      console.log(`   Result: ${navamsaTestPassed ? '✅ PASSED' : '❌ FAILED'}`);
    }

    // 3. Test Basic Chart Data for comparison
    console.log('\n📊 BASIC CHART CALCULATION RESULTS:');
    console.log('=' .repeat(50));
    console.log('🌅 Ascendant (Lagna):', basicChartData.ascendant);
    console.log('🌙 Moon Sign:', basicChartData.moonSign);
    console.log('☀️ Sun Sign:', basicChartData.sunSign);

    // Final Test Summary
    console.log('\n🏆 FINAL TEST SUMMARY:');
    console.log('=' .repeat(60));
    console.log(`🎯 Lagna Test: ${lagnaTestPassed ? '✅ PASSED' : '❌ FAILED'}`);
    console.log(`🎯 Navamsa Test: ${navamsaTestPassed ? '✅ PASSED' : '❌ FAILED'}`);

    const overallTestPassed = lagnaTestPassed && navamsaTestPassed;
    console.log(`\n🏆 OVERALL RESULT: ${overallTestPassed ? '✅ ALL TESTS PASSED' : '❌ TESTS FAILED'}`);

    if (!overallTestPassed) {
      console.log('\n🔧 DEBUGGING RECOMMENDATIONS:');
      if (!lagnaTestPassed) {
        console.log('   ❌ Lagna calculation needs fixing');
        console.log('   🔍 Check ascendant calculation algorithm');
        console.log('   🔍 Verify sidereal time calculation');
        console.log('   🔍 Check coordinate system and timezone handling');
      }
      if (!navamsaTestPassed) {
        console.log('   ❌ Navamsa calculation needs fixing');
        console.log('   🔍 Check Navamsa formula implementation');
        console.log('   🔍 Verify D-9 divisional chart logic');
        console.log('   🔍 Check ascendant navamsa position calculation');
      }
    }

    console.log('\n📊 ASHTAKAVARGA ANALYSIS:');
    console.log('=' .repeat(50));
    if (enhancedChartData.ashtakavarga && enhancedChartData.ashtakavarga.sarvashtakavarga) {
      console.log('🎯 Sarvashtakavarga totals:');
      enhancedChartData.ashtakavarga.sarvashtakavarga.forEach((total, index) => {
        console.log(`   House ${index + 1}: ${total} points`);
      });

      const totalPoints = enhancedChartData.ashtakavarga.sarvashtakavarga.reduce((sum, points) => sum + points, 0);
      console.log(`\n📊 Total Ashtakavarga Points: ${totalPoints}`);
      console.log('📝 Expected range: 290-340 points (traditional range)');

      if (totalPoints >= 290 && totalPoints <= 340) {
        console.log('✅ ASHTAKAVARGA CALCULATION LOOKS REASONABLE!');
      } else {
        console.log('⚠️ ASHTAKAVARGA TOTALS OUTSIDE EXPECTED RANGE');
      }
    }

    // Save the birth chart to database
    console.log('\n💾 Saving birth chart to database...');
    const birthChart = await prisma.birthChart.upsert({
      where: { userId: testUser.id },
      update: {
        birthDateTime: birthDateTime,
        birthPlace: testUser.birthPlace,
        birthLatitude: testUser.birthLatitude,
        birthLongitude: testUser.birthLongitude,
        timezone: timezone,
        planetPositions: enhancedChartData.planets,
        housePositions: enhancedChartData.houses,
        aspects: enhancedChartData.aspects,
        nakshatras: enhancedChartData.nakshatras,
        dashas: enhancedChartData.dashas,
        ascendant: enhancedChartData.ascendant,
        moonSign: enhancedChartData.moonSign,
        sunSign: enhancedChartData.sunSign,
        lagnaChart: enhancedChartData.lagnaChart,
        navamsaChart: enhancedChartData.navamsaChart,
        chandraChart: enhancedChartData.chandraChart,
        karakTable: enhancedChartData.karakTable,
        avasthaTable: enhancedChartData.avasthaTable,
        planetaryDetails: enhancedChartData.planetaryDetails,
        vimshottariDasha: enhancedChartData.vimshottariDasha,
        ashtakavarga: enhancedChartData.ashtakavarga,
        panchang: enhancedChartData.panchang,
        doshaAnalysis: enhancedChartData.doshaAnalysis,
        yogaAnalysis: enhancedChartData.yogaAnalysis,
        planetaryStrengths: enhancedChartData.planetaryStrengths,
        divisionalCharts: enhancedChartData.divisionalCharts,
        generalReading: enhancedChartData.generalReading,
        strengthsWeaknesses: enhancedChartData.strengthsWeaknesses,
        careerGuidance: enhancedChartData.careerGuidance,
        relationshipGuidance: enhancedChartData.relationshipGuidance,
        healthGuidance: enhancedChartData.healthGuidance,
        readingsEn: enhancedChartData.readingsEn,
        readingsSi: enhancedChartData.readingsSi,
        calculatedAt: new Date(),
        updatedAt: new Date()
      },
      create: {
        userId: testUser.id,
        birthDateTime: birthDateTime,
        birthPlace: testUser.birthPlace,
        birthLatitude: testUser.birthLatitude,
        birthLongitude: testUser.birthLongitude,
        timezone: timezone,
        planetPositions: enhancedChartData.planets,
        housePositions: enhancedChartData.houses,
        aspects: enhancedChartData.aspects,
        nakshatras: enhancedChartData.nakshatras,
        dashas: enhancedChartData.dashas,
        ascendant: enhancedChartData.ascendant,
        moonSign: enhancedChartData.moonSign,
        sunSign: enhancedChartData.sunSign,
        lagnaChart: enhancedChartData.lagnaChart,
        navamsaChart: enhancedChartData.navamsaChart,
        chandraChart: enhancedChartData.chandraChart,
        karakTable: enhancedChartData.karakTable,
        avasthaTable: enhancedChartData.avasthaTable,
        planetaryDetails: enhancedChartData.planetaryDetails,
        vimshottariDasha: enhancedChartData.vimshottariDasha,
        ashtakavarga: enhancedChartData.ashtakavarga,
        panchang: enhancedChartData.panchang,
        doshaAnalysis: enhancedChartData.doshaAnalysis,
        yogaAnalysis: enhancedChartData.yogaAnalysis,
        planetaryStrengths: enhancedChartData.planetaryStrengths,
        divisionalCharts: enhancedChartData.divisionalCharts,
        generalReading: enhancedChartData.generalReading,
        strengthsWeaknesses: enhancedChartData.strengthsWeaknesses,
        careerGuidance: enhancedChartData.careerGuidance,
        relationshipGuidance: enhancedChartData.relationshipGuidance,
        healthGuidance: enhancedChartData.healthGuidance,
        readingsEn: enhancedChartData.readingsEn,
        readingsSi: enhancedChartData.readingsSi,
        calculatedAt: new Date()
      }
    });

    console.log('✅ Birth chart saved with ID:', birthChart.id);

    console.log('\n🌐 TEST ACCESS:');
    console.log('=' .repeat(50));
    console.log('🔗 QR Token: navamsa-test-2024');
    console.log('🌐 Test URL: http://localhost:3000/auth?token=navamsa-test-2024');
    console.log('📱 Navigate to Horoscope tab to view the birth charts');

    await prisma.$disconnect();

  } catch (error) {
    console.error('❌ Error in Navamsa calculation test:', error);
    throw error;
  }
}

// Run the test
testNavamsaCalculation()
  .then(() => {
    console.log('\n🎉 Navamsa calculation test completed!');
    process.exit(0);
  })
  .catch((error) => {
    console.error('💥 Test failed:', error);
    process.exit(1);
  });
